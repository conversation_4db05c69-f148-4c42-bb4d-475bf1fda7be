/* visualizer.css */

/*
  为我们将要创建的canvas元素定义样式。
  ID选择器 #audio-visualizer-canvas 用于精确匹配我们用JS创建的画布。
*/
#audio-visualizer-canvas {
  /*
    position: absolute;
    将画布从正常的文档流中移除，使其可以相对于其父容器（视频播放器容器）进行定位。
    这是实现覆盖效果的基础。
  */
  position: absolute;

  /*
    top: 0; left: 0;
    将画布的左上角与父容器的左上角对齐。
  */
  top: 0;
  left: 0;

  /*
    确保canvas不会被CSS压缩
  */
  min-width: 100%;
  min-height: 100%;
  max-width: 100%;
  max-height: 100%;

  /*
    z-index: 10;
    设置堆叠顺序。一个较高的z-index可以确保我们的画布显示在视频内容之上。
    这个值可能需要根据YouTube页面的具体实现进行微调。
  */
  z-index: 10;

  /*
    pointer-events: none;
    这是实现"幽灵层"最关键的属性。
    它告诉浏览器，这个元素不应成为鼠标事件的目标。
    所有点击、悬停等事件都将"穿透"这个画布，直接作用于其下方的YouTube播放器控件。
  */
  pointer-events: none;

  /*
    opacity: 0.8;
    设置轻微的透明度，使可视化效果不会完全遮挡视频内容
  */
  opacity: 0.8;

  /* 临时调试样式 */
  border: 2px solid red !important;
  background-color: rgba(0, 0, 0, 0.3) !important;
}
