// content.js

// 使用一个立即执行函数表达式 (IIFE) 来封装代码，避免污染全局作用域。
(() => {
  console.log("YouTube Aural-Visual Bridge content script loaded.");

  // 全局变量，用于存储对关键对象的引用
  let audioContext;
  let analyser;
  let sourceNode;
  let canvas, canvasCtx;
  let animationFrameId;

  // 用于存储音频数据的数组
  let timeDomainData, frequencyData;

  // 标记是否已经初始化，防止重复设置
  const initializedVideos = new WeakSet();

  /**
   * 初始化Web Audio API和Canvas
   * @param {HTMLVideoElement} videoElement - 目标视频元素
   */
  function setupVisualizer(videoElement) {
    if (initializedVideos.has(videoElement)) {
      // 如果这个视频元素已经初始化过了，就直接返回
      return;
    }

    console.log("🎵 Setting up visualizer for video element:", videoElement);
    console.log("🎵 Video readyState:", videoElement.readyState);
    console.log("🎵 Video paused:", videoElement.paused);

    // --- 1. Web Audio API 设置 ---
    // 创建一个音频上下文
    audioContext = new (window.AudioContext || window.webkitAudioContext)();

    // 从<video>元素创建音频源节点
    // 注意：如果音频源已存在，需要先断开连接或重新创建上下文
    // 为简单起见，这里假设每次都是新的。在复杂场景下可能需要更精细的管理。
    try {
        sourceNode = audioContext.createMediaElementSource(videoElement);
    } catch (e) {
        console.error("Error creating media element source:", e);
        // 如果视频元素已经关联到另一个AudioContext，会抛出错误。
        // 在YouTube页面导航中，这可能发生。简单的处理是直接返回。
        return;
    }

    // 创建分析器节点
    analyser = audioContext.createAnalyser();

    // 配置分析器参数
    analyser.fftSize = 2048; // FFT大小，决定频率分辨率
    analyser.smoothingTimeConstant = 0.8; // 时间平滑，使可视化更流畅

    // 创建用于存储数据的数组
    const bufferLength = analyser.frequencyBinCount;
    timeDomainData = new Uint8Array(bufferLength);
    frequencyData = new Uint8Array(bufferLength);

    // 连接音频图：Source -> Analyser -> Destination(speakers)
    sourceNode.connect(analyser);
    analyser.connect(audioContext.destination);

    // --- 2. Canvas 设置 ---
    // 尝试多种可能的容器选择器
    let videoContainer = videoElement.closest('.html5-video-container') ||
                        videoElement.closest('.video-stream') ||
                        videoElement.closest('#movie_player') ||
                        videoElement.closest('.ytp-player-content') ||
                        videoElement.parentElement;

    console.log("🎵 Found video container:", videoContainer);
    console.log("🎵 Container class list:", videoContainer?.classList);

    if (!videoContainer) {
        console.error("❌ Could not find any suitable video container.");
        return;
    }

    // 创建canvas元素
    canvas = document.createElement('canvas');
    canvas.id = 'audio-visualizer-canvas';

    // 添加一些调试样式，让canvas更容易看到
    canvas.style.border = '2px solid red'; // 临时调试边框
    canvas.style.backgroundColor = 'rgba(0, 0, 0, 0.3)'; // 临时背景

    videoContainer.appendChild(canvas);
    canvasCtx = canvas.getContext('2d');

    console.log("🎵 Canvas created and added to container");
    console.log("🎵 Canvas dimensions:", canvas.width, "x", canvas.height);

    // --- 3. 响应式尺寸调整 ---
    const resizeObserver = new ResizeObserver(entries => {
      for (let entry of entries) {
        const { width, height } = entry.contentRect;
        canvas.width = width;
        canvas.height = height;
        console.log("🎵 Canvas resized to:", width, "x", height);
      }
    });
    resizeObserver.observe(videoContainer);

    // 初始设置canvas尺寸
    canvas.width = videoContainer.clientWidth || 640;
    canvas.height = videoContainer.clientHeight || 360;
    console.log("🎵 Initial canvas size:", canvas.width, "x", canvas.height);

    // 标记此视频已初始化
    initializedVideos.add(videoElement);

    // --- 4. 启动动画 ---
    // 确保之前的动画循环已停止
    if (animationFrameId) {
        cancelAnimationFrame(animationFrameId);
    }

    console.log("🎵 Starting animation loop...");
    draw();
  }

  /**
   * 动画循环的核心函数
   */
  function draw() {
    // 请求下一帧动画
    animationFrameId = requestAnimationFrame(draw);

    if (!analyser || !canvasCtx) {
      console.log("❌ Missing analyser or canvas context");
      return;
    }

    // 获取最新的音频数据
    analyser.getByteTimeDomainData(timeDomainData);
    analyser.getByteFrequencyData(frequencyData);

    // 检查是否有音频数据
    const hasAudio = frequencyData.some(value => value > 0);
    if (!hasAudio) {
      // 绘制一个测试矩形，确保canvas正在工作
      canvasCtx.fillStyle = 'rgba(255, 0, 0, 0.5)';
      canvasCtx.fillRect(10, 10, 100, 50);
      canvasCtx.fillStyle = 'white';
      canvasCtx.font = '16px Arial';
      canvasCtx.fillText('No Audio Data', 20, 35);
    }

    // 清除画布
    canvasCtx.clearRect(0, 0, canvas.width, canvas.height);

    // 绘制可视化图形
    drawFrequencyDomain(frequencyData);
    drawTimeDomain(timeDomainData);

    // 绘制调试信息
    drawDebugInfo();
  }

  /**
   * 绘制时域数据（波形图）
   * @param {Uint8Array} dataArray - 时域数据
   */
  function drawTimeDomain(dataArray) {
    canvasCtx.lineWidth = 2;
    canvasCtx.strokeStyle = 'rgba(255, 255, 255, 0.7)';
    canvasCtx.beginPath();

    const sliceWidth = canvas.width * 1.0 / analyser.frequencyBinCount;
    let x = 0;

    for (let i = 0; i < analyser.frequencyBinCount; i++) {
      const v = dataArray[i] / 128.0; // 归一化到 0-2 范围
      const y = v * canvas.height / 2;

      if (i === 0) {
        canvasCtx.moveTo(x, y);
      } else {
        canvasCtx.lineTo(x, y);
      }
      x += sliceWidth;
    }

    canvasCtx.lineTo(canvas.width, canvas.height / 2);
    canvasCtx.stroke();
  }

  /**
   * 绘制频域数据（频谱图）并应用功能性色彩系统
   * @param {Uint8Array} dataArray - 频域数据
   */
  function drawFrequencyDomain(dataArray) {
    const bufferLength = analyser.frequencyBinCount;
    const barWidth = (canvas.width / bufferLength) * 1.5;
    let x = 0;

    const highFrequencyThreshold = 4000; // 4kHz作为高频警示阈值
    const nyquist = audioContext.sampleRate / 2; // 奈奎斯特频率

    for (let i = 0; i < bufferLength; i++) {
      const barHeight = dataArray[i] * (canvas.height / 255.0) * 0.5; // 限制最大高度为屏幕一半

      // 计算当前bar代表的频率
      const currentFrequency = i * nyquist / bufferLength;

      let r, g, b;
      const intensity = dataArray[i] / 255; // 0 to 1

      if (currentFrequency > highFrequencyThreshold) {
        // 高频警示区：从黄到红
        r = 255;
        g = 255 * (1 - intensity); // 强度越高，绿色越少，越偏红
        b = 0;
      } else {
        // 中低频环境区：蓝色
        r = 50;
        g = 150 * intensity; // 强度越高，绿色越多，越偏青色
        b = 255;
      }

      canvasCtx.fillStyle = `rgb(${r},${g},${b})`;
      canvasCtx.fillRect(x, canvas.height - barHeight, barWidth, barHeight);

      x += barWidth + 1; // bar之间留1px间隙
    }
  }

  /**
   * 绘制调试信息
   */
  function drawDebugInfo() {
    if (!canvasCtx) return;

    canvasCtx.fillStyle = 'rgba(0, 0, 0, 0.7)';
    canvasCtx.fillRect(10, 10, 300, 100);

    canvasCtx.fillStyle = 'white';
    canvasCtx.font = '12px Arial';
    canvasCtx.fillText('🎵 Audio Visualizer Active', 20, 30);
    canvasCtx.fillText(`Canvas: ${canvas.width}x${canvas.height}`, 20, 50);
    canvasCtx.fillText(`AudioContext: ${audioContext?.state}`, 20, 70);

    // 显示频率数据的最大值
    const maxFreq = Math.max(...frequencyData);
    canvasCtx.fillText(`Max Frequency: ${maxFreq}`, 20, 90);
  }

  /**
   * MutationObserver的回调函数，用于检测DOM变化
   * @param {MutationRecord} mutationsList - 变化记录列表
   */
  function handleMutations(mutationsList) {
    for (const mutation of mutationsList) {
      if (mutation.type === 'childList') {
        mutation.addedNodes.forEach(node => {
          // 检查添加的节点本身是否是<video>或其内部是否包含<video>
          if (node.nodeType === 1) { // 确保是元素节点
            let videoElement = null;
            if (node.tagName === 'VIDEO') {
              videoElement = node;
            } else {
              videoElement = node.querySelector('video');
            }

            if (videoElement) {
              // 等待视频可以播放时再设置，确保音频数据可用
              videoElement.addEventListener('canplay', () => {
                setupVisualizer(videoElement);
              }, { once: true });
            }
          }
        });
      }
    }
  }

  // --- 5. 启动观察者 ---
  // 创建并配置MutationObserver
  const observer = new MutationObserver(handleMutations);
  observer.observe(document.body, {
    childList: true,
    subtree: true
  });

  // 初始检查页面上是否已存在视频
  console.log("🎵 Checking for existing video elements...");
  const existingVideo = document.querySelector('video');
  if (existingVideo) {
    console.log("🎵 Found existing video:", existingVideo);
    console.log("🎵 Video src:", existingVideo.src || existingVideo.currentSrc);
    if (existingVideo.readyState >= 3) { // HAVE_FUTURE_DATA
        console.log("🎵 Video ready, setting up visualizer immediately");
        setupVisualizer(existingVideo);
    } else {
        console.log("🎵 Video not ready, waiting for canplay event");
        existingVideo.addEventListener('canplay', () => {
            console.log("🎵 Video canplay event fired");
            setupVisualizer(existingVideo);
        }, { once: true });
    }
  } else {
    console.log("🎵 No existing video found, waiting for DOM changes");
  }

  // 添加一个简单的测试，确保脚本正在运行
  console.log("🎵 YouTube Aural-Visual Bridge script fully loaded and active");

})();
